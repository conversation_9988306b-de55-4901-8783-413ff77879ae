"use client";

import { But<PERSON> } from "@/components/ui/button";
import { AlertCircle, RefreshCw, Home } from "lucide-react";
import Link from "next/link";
import { useEffect } from "react";

interface DashboardErrorProps {
  error: Error & { digest?: string };
  reset: () => void;
}

export default function DashboardError({ error, reset }: DashboardErrorProps) {
  useEffect(() => {
    // Log the error to an error reporting service
    console.error("Dashboard error:", error);
  }, [error]);

  return (
    <div className="container mx-auto px-4 py-8 min-h-screen max-w-7xl">
      <div className="flex items-center justify-center min-h-[60vh]">
        <div className="text-center space-y-6 max-w-md">
          <div className="space-y-4">
            <AlertCircle className="h-16 w-16 text-red-500 mx-auto" />
            <div className="space-y-2">
              <h1 className="text-2xl font-bold text-foreground">
                Oops! Ceva nu a mers bine
              </h1>
              <p className="text-muted-foreground">
                A apărut o eroare neașteptată în dashboard. Vă rugăm să încercați din nou.
              </p>
            </div>
          </div>

          {/* Error details for development */}
          {process.env.NODE_ENV === "development" && (
            <div className="bg-muted/50 rounded-lg p-4 text-left">
              <p className="text-sm font-mono text-red-600 dark:text-red-400">
                {error.message}
              </p>
              {error.digest && (
                <p className="text-xs text-muted-foreground mt-2">
                  Error ID: {error.digest}
                </p>
              )}
            </div>
          )}

          <div className="flex flex-col sm:flex-row gap-3 justify-center">
            <Button onClick={reset} className="gap-2">
              <RefreshCw className="h-4 w-4" />
              Încearcă din nou
            </Button>
            <Button variant="outline" asChild className="gap-2">
              <Link href="/portfolios">
                <Home className="h-4 w-4" />
                Înapoi la portofolii
              </Link>
            </Button>
          </div>

          <p className="text-xs text-muted-foreground">
            Dacă problema persistă, vă rugăm să ne contactați la{" "}
            <Link 
              href="/contact" 
              className="text-portavio-orange hover:underline"
            >
              pagina de contact
            </Link>
            .
          </p>
        </div>
      </div>
    </div>
  );
}
