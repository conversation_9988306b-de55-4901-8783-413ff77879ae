"use client";

import { useState } from "react";
import { BarChart3, TrendingUp } from "lucide-react";
import { PortfolioMultiSelect } from "@/components/dashboard/portfolio-multi-select";
import { TimePeriodSelector } from "@/components/dashboard/time-period-selector";
import { PerformanceChart } from "@/components/dashboard/performance-chart";
import { TimePeriod } from "@/utils/db/portfolio-performance-queries";

export default function DashboardClientPage() {
  const [selectedPortfolios, setSelectedPortfolios] = useState<string[]>([]);
  const [selectedPeriod, setSelectedPeriod] = useState<TimePeriod>("1M");

  return (
    <div className="container mx-auto px-4 py-8 min-h-screen max-w-7xl">
      <div className="space-y-6">
        {/* Header Section */}
        <div className="flex items-start justify-between flex-wrap gap-4">
          <div className="min-w-0 flex-1">
            <div className="flex items-center gap-3">
              <BarChart3 className="h-6 w-6 sm:h-8 sm:w-8 text-portavio-blue flex-shrink-0" />
              <h1 className="text-2xl sm:text-3xl font-bold text-foreground truncate">
                Dashboard
              </h1>
            </div>
            <p className="text-sm sm:text-base text-muted-foreground mt-2">
              Urmărește performanța portofoliilor tale
            </p>
          </div>
        </div>

        {/* Portfolio Selector */}
        <div className="space-y-3">
          <h2 className="text-base sm:text-lg font-semibold">Portofolii:</h2>
          <div className="w-full sm:max-w-md">
            <PortfolioMultiSelect
              value={selectedPortfolios}
              onValueChange={setSelectedPortfolios}
            />
          </div>
        </div>

        {/* Performance Card */}
        <div className="bg-card border rounded-lg p-4 sm:p-6">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6">
            <div className="flex items-center gap-3 min-w-0">
              <TrendingUp className="h-5 w-5 sm:h-6 sm:w-6 text-portavio-orange flex-shrink-0" />
              <h3 className="text-lg sm:text-xl font-semibold truncate">
                Performanța portofoliului
              </h3>
            </div>
            <div className="flex-shrink-0">
              <TimePeriodSelector
                value={selectedPeriod}
                onValueChange={setSelectedPeriod}
              />
            </div>
          </div>

          {/* Performance Chart */}
          <PerformanceChart
            portfolios={selectedPortfolios}
            period={selectedPeriod}
          />
        </div>
      </div>
    </div>
  );
}
