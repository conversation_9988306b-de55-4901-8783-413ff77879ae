import { hasuraQuery } from "./hasura";
import { Transaction, AssetPrice } from "./portfolio-queries";

// Types for portfolio performance calculations
export interface PortfolioPerformanceData {
  date: string;
  value: number;
  change: number;
  changePercent: number;
}

export interface AssetHolding {
  ticker: string;
  quantity: number;
  averagePrice: number;
  currentPrice?: number;
  value?: number;
}

export interface PortfolioSnapshot {
  date: string;
  holdings: AssetHolding[];
  totalValue: number;
}

export type TimePeriod = "1W" | "1M" | "YTD" | "1Y";

// GraphQL Queries

/**
 * Get historical asset prices for a specific asset within a date range
 */
export const GET_ASSET_PRICES_BY_DATE_RANGE = `
  query GetAssetPricesByDateRange($assetId: Int!, $startDate: date!, $endDate: date!) {
    ptvuser_asset_price(
      where: {
        asset_id: {_eq: $assetId}
        date: {_gte: $startDate, _lte: $endDate}
      }
      order_by: {date: asc}
    ) {
      price_id
      asset_id
      date
      open_price
      high_price
      low_price
      close_price
      adj_close
      volume
      created_at
      updated_at
    }
  }
`;

/**
 * Get multiple assets' prices for a specific date (or closest available date)
 */
export const GET_ASSETS_PRICES_BY_DATE = `
  query GetAssetsPricesByDate($assetIds: [Int!]!, $targetDate: date!) {
    ptvuser_asset_price(
      where: {
        asset_id: {_in: $assetIds}
        date: {_lte: $targetDate}
      }
      order_by: {date: desc}
      distinct_on: [date]
    ) {
      price_id
      asset_id
      date
      close_price
      adj_close
    }
  }
`;

/**
 * Get asset IDs by tickers
 */
export const GET_ASSET_IDS_BY_TICKERS = `
  query GetAssetIdsByTickers($tickers: [String!]!) {
    ptvuser_asset(
      where: {ticker: {_in: $tickers}}
    ) {
      asset_id
      ticker
    }
  }
`;

/**
 * Get transactions for multiple portfolios within a date range
 */
export const GET_PORTFOLIOS_TRANSACTIONS_BY_DATE_RANGE = `
  query GetPortfoliosTransactionsByDateRange($portfolioIds: [uuid!]!, $startDate: date!, $endDate: date!) {
    ptvuser_transactions(
      where: {
        portfolio_id: {_in: $portfolioIds}
        transaction_date: {_gte: $startDate, _lte: $endDate}
      }
      order_by: {transaction_date: asc}
    ) {
      id
      portfolio_id
      ticker
      price
      quantity
      transaction_date
      transaction_type
      transaction_fee
      notes
      created_at
      updated_at
    }
  }
`;

// Utility Functions

/**
 * Get date range for a given time period
 */
export function getDateRangeForPeriod(period: TimePeriod): {
  startDate: string;
  endDate: string;
} {
  const endDate = new Date();
  const startDate = new Date();

  switch (period) {
    case "1W":
      startDate.setDate(endDate.getDate() - 7);
      break;
    case "1M":
      startDate.setMonth(endDate.getMonth() - 1);
      break;
    case "YTD":
      startDate.setMonth(0, 1); // January 1st of current year
      break;
    case "1Y":
      startDate.setFullYear(endDate.getFullYear() - 1);
      break;
  }

  return {
    startDate: startDate.toISOString().split("T")[0],
    endDate: endDate.toISOString().split("T")[0],
  };
}

/**
 * Get data point frequency for a given time period
 */
export function getDataPointFrequency(
  period: TimePeriod
): "daily" | "weekly" | "monthly" {
  switch (period) {
    case "1W":
    case "1M":
      return "daily";
    case "YTD":
      return "weekly";
    case "1Y":
      return "monthly";
    default:
      return "daily";
  }
}

/**
 * Get asset IDs for given tickers
 */
export async function getAssetIdsByTickers(
  tickers: string[]
): Promise<Map<string, number>> {
  try {
    const result = await hasuraQuery<{
      ptvuser_asset: Array<{ asset_id: number; ticker: string }>;
    }>(GET_ASSET_IDS_BY_TICKERS, {
      variables: { tickers: tickers.map((t) => t.toUpperCase()) },
    });

    const assetMap = new Map<string, number>();
    result.ptvuser_asset.forEach((asset) => {
      assetMap.set(asset.ticker, asset.asset_id);
    });

    return assetMap;
  } catch (error) {
    console.error("Error fetching asset IDs by tickers:", error);
    throw new Error("Nu s-au putut încărca ID-urile activelor");
  }
}

/**
 * Get historical asset prices for a date range
 */
export async function getAssetPricesByDateRange(
  assetId: number,
  startDate: string,
  endDate: string
): Promise<AssetPrice[]> {
  try {
    const result = await hasuraQuery<{
      ptvuser_asset_price: AssetPrice[];
    }>(GET_ASSET_PRICES_BY_DATE_RANGE, {
      variables: { assetId, startDate, endDate },
    });

    return result.ptvuser_asset_price || [];
  } catch (error) {
    console.error("Error fetching asset prices by date range:", error);
    throw new Error("Nu s-au putut încărca prețurile istorice");
  }
}

/**
 * Get asset prices for multiple assets on a specific date (or closest available)
 */
export async function getAssetsPricesByDate(
  assetIds: number[],
  targetDate: string
): Promise<Map<number, { price: number; date: string }>> {
  try {
    const result = await hasuraQuery<{
      ptvuser_asset_price: Array<{
        asset_id: number;
        date: string;
        close_price: number;
      }>;
    }>(GET_ASSETS_PRICES_BY_DATE, {
      variables: { assetIds, targetDate },
    });

    console.log(result.ptvuser_asset_price, targetDate);

    const priceMap = new Map<number, { price: number; date: string }>();
    result.ptvuser_asset_price.forEach((price) => {
      priceMap.set(price.asset_id, {
        price: price.close_price,
        date: price.date,
      });
    });

    return priceMap;
  } catch (error) {
    console.error("Error fetching assets prices by date:", error);
    throw new Error("Nu s-au putut încărca prețurile pentru data specificată");
  }
}

/**
 * Get transactions for multiple portfolios within a date range
 */
export async function getPortfoliosTransactionsByDateRange(
  portfolioIds: string[],
  startDate: string,
  endDate: string
): Promise<Transaction[]> {
  try {
    const result = await hasuraQuery<{
      ptvuser_transactions: Transaction[];
    }>(GET_PORTFOLIOS_TRANSACTIONS_BY_DATE_RANGE, {
      variables: { portfolioIds, startDate, endDate },
    });

    return result.ptvuser_transactions || [];
  } catch (error) {
    console.error(
      "Error fetching portfolios transactions by date range:",
      error
    );
    throw new Error(
      "Nu s-au putut încărca tranzacțiile pentru perioada specificată"
    );
  }
}

/**
 * Calculate portfolio holdings at a specific date
 */
export function calculatePortfolioHoldingsAtDate(
  transactions: Transaction[],
  targetDate: string
): Map<string, AssetHolding> {
  const holdings = new Map<string, AssetHolding>();

  // Filter transactions up to the target date
  const relevantTransactions = transactions.filter(
    (t) => t.transaction_date <= targetDate
  );

  // Calculate holdings for each ticker
  relevantTransactions.forEach((transaction) => {
    const { ticker, quantity, price, transaction_type } = transaction;

    if (!holdings.has(ticker)) {
      holdings.set(ticker, {
        ticker,
        quantity: 0,
        averagePrice: 0,
      });
    }

    const holding = holdings.get(ticker)!;

    if (transaction_type === "BUY") {
      // Calculate new average price for BUY transactions
      const totalValue =
        holding.quantity * holding.averagePrice + quantity * price;
      const totalQuantity = holding.quantity + quantity;

      holding.quantity = totalQuantity;
      holding.averagePrice = totalQuantity > 0 ? totalValue / totalQuantity : 0;
    } else if (transaction_type === "SELL") {
      // For SELL transactions, reduce quantity but keep average price
      holding.quantity = Math.max(0, holding.quantity - quantity);
    }
  });

  // Remove holdings with zero quantity
  const activeHoldings = new Map<string, AssetHolding>();
  holdings.forEach((holding, ticker) => {
    if (holding.quantity > 0) {
      activeHoldings.set(ticker, holding);
    }
  });

  return activeHoldings;
}

/**
 * Calculate portfolio performance over time
 */
export async function calculatePortfolioPerformance(
  portfolioIds: string[],
  period: TimePeriod
): Promise<PortfolioPerformanceData[]> {
  try {
    const { startDate, endDate } = getDateRangeForPeriod(period);
    const frequency = getDataPointFrequency(period);

    // Get all transactions for the portfolios in the date range
    const transactions = await getPortfoliosTransactionsByDateRange(
      portfolioIds,
      startDate,
      endDate
    );

    if (transactions.length === 0) {
      return [];
    }

    // Get all unique tickers from transactions
    const uniqueTickers = Array.from(
      new Set(transactions.map((t) => t.ticker))
    );

    // Get asset IDs for all tickers
    const assetIdMap = await getAssetIdsByTickers(uniqueTickers);
    const assetIds = Array.from(assetIdMap.values());

    // Generate date points based on frequency
    const datePoints = generateDatePoints(startDate, endDate, frequency);

    // Calculate portfolio value for each date point
    const performanceData: PortfolioPerformanceData[] = [];
    let previousValue = 0;

    for (const date of datePoints) {
      // Calculate holdings at this date
      const holdings = calculatePortfolioHoldingsAtDate(transactions, date);

      if (holdings.size === 0) {
        performanceData.push({
          date,
          value: 0,
          change: 0,
          changePercent: 0,
        });
        continue;
      }

      // Get asset prices for this date
      const assetPrices = await getAssetsPricesByDate(assetIds, date);

      // Calculate total portfolio value
      let totalValue = 0;
      holdings.forEach((holding, ticker) => {
        const assetId = assetIdMap.get(ticker);
        if (assetId) {
          const priceData = assetPrices.get(assetId);
          if (priceData) {
            holding.currentPrice = priceData.price;
            holding.value = holding.quantity * priceData.price;
            totalValue += holding.value;
          }
        }
      });

      // Calculate change from previous value
      const change = totalValue - previousValue;
      const changePercent =
        previousValue > 0 ? (change / previousValue) * 100 : 0;

      performanceData.push({
        date,
        value: totalValue,
        change,
        changePercent,
      });

      previousValue = totalValue;
    }

    return performanceData;
  } catch (error) {
    console.error("Error calculating portfolio performance:", error);
    throw new Error("Nu s-a putut calcula performanța portofoliului");
  }
}

/**
 * Generate date points based on frequency
 */
function generateDatePoints(
  startDate: string,
  endDate: string,
  frequency: "daily" | "weekly" | "monthly"
): string[] {
  const dates: string[] = [];
  const start = new Date(startDate);
  const end = new Date(endDate);
  const current = new Date(start);

  while (current <= end) {
    dates.push(current.toISOString().split("T")[0]);

    switch (frequency) {
      case "daily":
        current.setDate(current.getDate() + 1);
        break;
      case "weekly":
        current.setDate(current.getDate() + 7);
        break;
      case "monthly":
        current.setMonth(current.getMonth() + 1);
        break;
    }
  }

  return dates;
}
