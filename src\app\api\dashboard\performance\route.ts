import { auth } from "@/lib/auth";
import { headers } from "next/headers";
import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import {
  calculatePortfolioPerformance,
  TimePeriod,
} from "@/utils/db/portfolio-performance-queries";
import { getUserPortfolios } from "@/utils/db/portfolio-queries";

// Validation schema for query parameters
const performanceQuerySchema = z.object({
  portfolios: z
    .string()
    .optional()
    .transform((val) => {
      if (!val) return [];
      try {
        const parsed = JSON.parse(val);
        return Array.isArray(parsed) ? parsed : [];
      } catch {
        return [];
      }
    }),
  period: z
    .enum(["1W", "1M", "YTD", "1Y"])
    .default("1M"),
});

// GET /api/dashboard/performance - Get portfolio performance data
export async function GET(request: NextRequest) {
  try {
    const session = await auth.api.getSession({
      headers: await headers(),
    });

    if (!session?.user) {
      return NextResponse.json(
        { error: "Nu ești autentificat" },
        { status: 401 }
      );
    }

    // Parse and validate query parameters
    const { searchParams } = new URL(request.url);
    const portfoliosParam = searchParams.get("portfolios");
    const periodParam = searchParams.get("period");

    let validatedParams;
    try {
      validatedParams = performanceQuerySchema.parse({
        portfolios: portfoliosParam,
        period: periodParam,
      });
    } catch (error) {
      if (error instanceof z.ZodError) {
        return NextResponse.json(
          { 
            error: "Parametri invalizi",
            details: error.errors.map(e => e.message).join(", ")
          },
          { status: 400 }
        );
      }
      throw error;
    }

    const { portfolios: selectedPortfolioIds, period } = validatedParams;

    // Get user's portfolios to validate access
    const userPortfolios = await getUserPortfolios(session.user.id);
    const userPortfolioIds = userPortfolios.map(p => p.id);

    // Determine which portfolios to analyze
    let portfolioIdsToAnalyze: string[];
    
    if (selectedPortfolioIds.length === 0) {
      // If no specific portfolios selected, use all user portfolios
      portfolioIdsToAnalyze = userPortfolioIds;
    } else {
      // Validate that selected portfolios belong to the user
      const validSelectedIds = selectedPortfolioIds.filter(id => 
        userPortfolioIds.includes(id)
      );
      
      if (validSelectedIds.length === 0) {
        return NextResponse.json(
          { error: "Nu ai acces la portofoliile selectate" },
          { status: 403 }
        );
      }
      
      portfolioIdsToAnalyze = validSelectedIds;
    }

    // Calculate portfolio performance
    const performanceData = await calculatePortfolioPerformance(
      portfolioIdsToAnalyze,
      period as TimePeriod
    );

    // Get portfolio names for response
    const analyzedPortfolios = userPortfolios.filter(p => 
      portfolioIdsToAnalyze.includes(p.id)
    );

    return NextResponse.json({
      success: true,
      data: performanceData,
      metadata: {
        period,
        portfolios: analyzedPortfolios.map(p => ({
          id: p.id,
          name: p.name,
        })),
        dataPoints: performanceData.length,
      },
    });
  } catch (error) {
    console.error("Error fetching portfolio performance:", error);
    
    // Handle specific error types
    if (error instanceof Error) {
      if (error.message.includes("Nu s-au putut încărca")) {
        return NextResponse.json(
          { error: error.message },
          { status: 500 }
        );
      }
    }
    
    return NextResponse.json(
      { error: "Nu s-au putut încărca datele de performanță" },
      { status: 500 }
    );
  }
}
