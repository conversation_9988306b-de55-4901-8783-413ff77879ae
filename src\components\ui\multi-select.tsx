"use client";

import * as React from "react";
import { Check, ChevronDown, X } from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Badge } from "@/components/ui/badge";

export interface MultiSelectOption {
  value: string;
  label: string;
  description?: string;
  disabled?: boolean;
}

interface MultiSelectProps {
  options: MultiSelectOption[];
  value: string[];
  onValueChange: (value: string[]) => void;
  placeholder?: string;
  emptyMessage?: string;
  disabled?: boolean;
  className?: string;
  maxDisplayItems?: number;
  selectAllLabel?: string;
  clearAllLabel?: string;
  showSelectAll?: boolean;
  showClearAll?: boolean;
}

export function MultiSelect({
  options,
  value,
  onValueChange,
  placeholder = "Selectează opțiuni...",
  emptyMessage = "Nu s-au găsit opțiuni.",
  disabled = false,
  className,
  maxDisplayItems = 3,
  selectAllLabel = "Selectează toate",
  clearAllLabel = "Șterge toate",
  showSelectAll = true,
  showClearAll = true,
}: MultiSelectProps) {
  const [open, setOpen] = React.useState(false);

  const selectedOptions = options.filter((option) =>
    value.includes(option.value)
  );

  const isAllSelected = options.length > 0 && value.length === options.length;
  const isNoneSelected = value.length === 0;

  const handleSelectAll = () => {
    if (isAllSelected) {
      onValueChange([]);
    } else {
      onValueChange(options.map((option) => option.value));
    }
  };

  const handleClearAll = () => {
    onValueChange([]);
  };

  const handleToggleOption = (optionValue: string) => {
    const newValue = value.includes(optionValue)
      ? value.filter((v) => v !== optionValue)
      : [...value, optionValue];
    onValueChange(newValue);
  };

  const handleRemoveOption = (optionValue: string) => {
    onValueChange(value.filter((v) => v !== optionValue));
  };

  const displayText = React.useMemo(() => {
    if (isNoneSelected) {
      return placeholder;
    }

    if (isAllSelected) {
      return selectAllLabel;
    }

    if (selectedOptions.length <= maxDisplayItems) {
      return selectedOptions.map((option) => option.label).join(", ");
    }

    return `${selectedOptions.length} selectate`;
  }, [
    isNoneSelected,
    isAllSelected,
    selectedOptions,
    maxDisplayItems,
    placeholder,
    selectAllLabel,
  ]);

  return (
    <div className={cn("space-y-2", className)}>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className="w-full justify-between"
            disabled={disabled}
          >
            <span className="truncate">{displayText}</span>
            <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-[var(--radix-popover-trigger-width)] p-0">
          <div className="max-h-60 overflow-auto">
            {options.length === 0 ? (
              <div className="py-6 text-center text-sm text-muted-foreground">
                {emptyMessage}
              </div>
            ) : (
              <div className="p-1">
                {/* Select All / Clear All buttons */}
                {(showSelectAll || showClearAll) && (
                  <div className="flex gap-1 p-1 border-b">
                    {showSelectAll && (
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-8 flex-1 text-xs"
                        onClick={handleSelectAll}
                      >
                        {isAllSelected ? "Deselectează toate" : selectAllLabel}
                      </Button>
                    )}
                    {showClearAll && !isNoneSelected && (
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-8 flex-1 text-xs"
                        onClick={handleClearAll}
                      >
                        {clearAllLabel}
                      </Button>
                    )}
                  </div>
                )}

                {/* Options */}
                {options.map((option) => {
                  const isSelected = value.includes(option.value);
                  return (
                    <div
                      key={option.value}
                      className={cn(
                        "relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none mb-1",
                        option.disabled
                          ? "pointer-events-none opacity-50"
                          : "cursor-pointer hover:bg-accent hover:text-accent-foreground",
                        isSelected && "bg-accent text-accent-foreground"
                      )}
                      onClick={() =>
                        !option.disabled && handleToggleOption(option.value)
                      }
                    >
                      <Check
                        className={cn(
                          "mr-2 h-4 w-4",
                          isSelected ? "opacity-100" : "opacity-0"
                        )}
                      />
                      <div className="flex-1">
                        <div>{option.label}</div>
                        {option.description && (
                          <div className="text-xs text-muted-foreground">
                            {option.description}
                          </div>
                        )}
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </div>
        </PopoverContent>
      </Popover>

      {/* Selected items as badges */}
      {selectedOptions.length > 0 && (
        <div className="flex flex-wrap gap-1">
          {selectedOptions.map((option) => (
            <Badge
              key={option.value}
              variant="secondary"
              className="text-xs"
            >
              {option.label}
              <button
                className="ml-1 ring-offset-background rounded-full outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
                onKeyDown={(e) => {
                  if (e.key === "Enter") {
                    handleRemoveOption(option.value);
                  }
                }}
                onMouseDown={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                }}
                onClick={() => handleRemoveOption(option.value)}
              >
                <X className="h-3 w-3 text-muted-foreground hover:text-foreground" />
              </button>
            </Badge>
          ))}
        </div>
      )}
    </div>
  );
}
