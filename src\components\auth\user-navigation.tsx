"use client";

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useAuth } from "@/hooks/use-auth";
import { useProfilePicture } from "@/hooks/use-profile-picture";
import { authUtils } from "@/lib/auth-utils";
import { BarChart3, BriefcaseBusiness, LogOut, User } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useRouter } from "next/navigation";

interface UserNavigationProps {
  isMobile?: boolean;
  onLinkClick?: () => void;
}

export function UserNavigation({
  isMobile = false,
  onLinkClick,
}: UserNavigationProps) {
  const { user, isAuthenticated, isLoading } = useAuth();
  const {
    profileImage,
    imageSourceType,
    isLoading: isLoadingPicture,
  } = useProfilePicture();
  const router = useRouter();

  const handleSignOut = async () => {
    try {
      await authUtils.signOut(() => router.refresh());
    } catch (error) {
      console.error("Sign out error:", error);
    }
  };

  const AuthLinks = ({ mobile = false }: { mobile?: boolean }) => {
    const linkClass = mobile
      ? "text-white hover:text-blue-200 transition-colors py-2 text-center"
      : "text-white hover:text-blue-200 transition-colors";

    const links = (
      <>
        <Link href="/auth/signin" className={linkClass} onClick={onLinkClick}>
          Loghează-te
        </Link>
        <Link href="/auth/signup" className={linkClass} onClick={onLinkClick}>
          Fă primul pas
        </Link>
      </>
    );

    return mobile ? (
      <div className="flex flex-col gap-4">{links}</div>
    ) : (
      <div className="flex items-center gap-4">{links}</div>
    );
  };

  const UserLinks = ({ mobile = false }: { mobile?: boolean }) => {
    const linkClass = mobile
      ? "text-white hover:text-blue-200 transition-colors py-2 text-center flex items-center gap-2"
      : "text-white hover:text-blue-200 transition-colors flex items-center gap-2";

    return (
      <>
        <Link href="/portfolios" className={linkClass} onClick={onLinkClick}>
          <BriefcaseBusiness className="h-4 w-4" />
          <span>Portofolii</span>
        </Link>
        <Link href="/profile" className={linkClass} onClick={onLinkClick}>
          <User className="h-4 w-4" />
          <span>Contul meu</span>
        </Link>
      </>
    );
  };

  const UserAvatar = ({ size = "default" }: { size?: "default" | "large" }) => {
    const avatarSize = size === "large" ? "h-10 w-10" : "h-8 w-8";
    const textSize = size === "large" ? "text-lg" : "text-sm";

    if (isLoadingPicture && !profileImage) {
      return (
        <div
          className={`${avatarSize} animate-pulse rounded-full bg-muted`}
        ></div>
      );
    }

    if (profileImage) {
      return (
        <Image
          src={profileImage}
          alt={user?.username || user?.name || "Avatar"}
          width={size === "large" ? 40 : 32}
          height={size === "large" ? 40 : 32}
          className={`${avatarSize} rounded-full object-cover`}
          priority={imageSourceType === "base64"}
          title={user?.username || user?.name || "Avatar"}
        />
      );
    }

    return (
      <div
        className={`flex ${avatarSize} items-center justify-center rounded-full bg-portavio-orange hover:bg-portavio-orange-hover text-white ${textSize} ${
          size === "large" ? "border-white" : "border-1 border-white"
        }`}
      >
        {user?.username?.[0]?.toUpperCase() ||
          user?.name?.[0]?.toUpperCase() ||
          "U"}
      </div>
    );
  };

  const DropdownContent = ({ align = "end" }: { align?: "start" | "end" }) => (
    <DropdownMenuContent
      className="w-fit w-max-[300px] p-2"
      align={align}
      forceMount
    >
      <DropdownMenuLabel className="font-normal">
        <div className="flex items-center gap-2">
          <UserAvatar size="default" />
          <div className="flex flex-col space-y-1">
            <p className="text-sm font-medium leading-none">
              {user?.name || user?.username}{" "}
              {user?.username ? `(${user?.username})` : ""}
            </p>
            <p className="text-xs leading-none text-muted-foreground">
              {user?.email}
            </p>
          </div>
        </div>
      </DropdownMenuLabel>
      <DropdownMenuSeparator />
      <DropdownMenuItem asChild>
        <Link href="/dashboard" className="cursor-pointer">
          <BarChart3 className="mr-2 h-4 w-4 dark:text-white" />
          <span>Dashboard</span>
        </Link>
      </DropdownMenuItem>
      <DropdownMenuItem asChild>
        <Link href="/portfolios" className="cursor-pointer">
          <BriefcaseBusiness className="mr-2 h-4 w-4 dark:text-white" />
          <span>Portofolii</span>
        </Link>
      </DropdownMenuItem>
      <DropdownMenuItem asChild>
        <Link href="/profile" className="cursor-pointer">
          <User className="mr-2 h-4 w-4 dark:text-white" />
          <span>Contul meu</span>
        </Link>
      </DropdownMenuItem>
      <DropdownMenuSeparator />
      <DropdownMenuItem onClick={handleSignOut} className="cursor-pointer">
        <LogOut className="mr-2 h-4 w-4 dark:text-white" />
        <span>Deconectează-te</span>
      </DropdownMenuItem>
    </DropdownMenuContent>
  );

  if (isLoading) {
    return <div className="h-8 w-8 animate-pulse rounded-full bg-muted"></div>;
  }

  if (!isAuthenticated) {
    return <AuthLinks mobile={isMobile} />;
  }

  if (isMobile) {
    return (
      <div className="flex flex-col gap-4">
        <div className="flex items-center gap-3 p-2 rounded-lg bg-white/5">
          <UserAvatar size="default" />
          <div className="flex flex-col items-start text-left min-w-0">
            <p className="text-sm font-medium leading-none text-white truncate">
              {user?.name || user?.username}
            </p>
            <p className="text-xs leading-none text-gray-300 truncate">
              {user?.email}
            </p>
          </div>
        </div>

        <div className="flex flex-col gap-2">
          <Link
            href="/portfolios"
            className="text-white hover:text-blue-200 transition-colors py-2 text-center flex items-center gap-2"
            onClick={onLinkClick}
          >
            <BriefcaseBusiness className="h-4 w-4" />
            <span>Portofolii</span>
          </Link>
          <Link
            href="/profile"
            className="text-white hover:text-blue-200 transition-colors py-2 text-center flex items-center gap-2"
            onClick={onLinkClick}
          >
            <User className="h-4 w-4" />
            <span>Contul meu</span>
          </Link>
          <button
            onClick={async () => {
              await handleSignOut();
              onLinkClick?.();
            }}
            className="text-white hover:text-blue-200 transition-colors py-2 text-center flex items-center gap-2 cursor-pointer"
          >
            <LogOut className="h-4 w-4" />
            <span>Deconectează-te</span>
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="flex items-center gap-4">
      <UserLinks />
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <button className="p-2 rounded-full hover:bg-white/10 transition-colors cursor-pointer">
            <UserAvatar size="large" />
          </button>
        </DropdownMenuTrigger>
        <DropdownContent align="end" />
      </DropdownMenu>
    </div>
  );
}
