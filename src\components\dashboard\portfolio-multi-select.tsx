"use client";

import { MultiSelect, MultiSelectOption } from "@/components/ui/multi-select";
import { usePortfoliosWithMetrics } from "@/hooks/use-portfolios-query";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import { AlertCircle } from "lucide-react";

interface PortfolioMultiSelectProps {
  value: string[];
  onValueChange: (value: string[]) => void;
  className?: string;
}

export function PortfolioMultiSelect({
  value,
  onValueChange,
  className,
}: PortfolioMultiSelectProps) {
  const { data, isLoading, error } = usePortfoliosWithMetrics();

  if (isLoading) {
    return (
      <div className="flex items-center gap-2">
        <LoadingSpinner size="sm" />
        <span className="text-sm text-muted-foreground">
          Se încarcă portofoliile...
        </span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center gap-2 text-red-600 dark:text-red-400">
        <AlertCircle className="h-4 w-4" />
        <span className="text-sm">
          Eroare la încărcarea portofoliilor
        </span>
      </div>
    );
  }

  if (!data?.portfolios || data.portfolios.length === 0) {
    return (
      <div className="text-sm text-muted-foreground">
        Nu ai portofolii create încă.
      </div>
    );
  }

  // Convert portfolios to multi-select options
  const options: MultiSelectOption[] = data.portfolios.map((portfolio) => ({
    value: portfolio.id,
    label: portfolio.name,
    description: portfolio.description || undefined,
  }));

  // If no portfolios are selected, default to all portfolios
  const effectiveValue = value.length === 0 ? options.map(opt => opt.value) : value;

  const handleValueChange = (newValue: string[]) => {
    // If all portfolios are selected, we can represent this as an empty array
    // to indicate "all portfolios" state
    if (newValue.length === options.length) {
      onValueChange([]);
    } else {
      onValueChange(newValue);
    }
  };

  return (
    <MultiSelect
      options={options}
      value={effectiveValue}
      onValueChange={handleValueChange}
      placeholder="Selectează portofolii..."
      emptyMessage="Nu s-au găsit portofolii."
      selectAllLabel="Toate portofoliile"
      clearAllLabel="Șterge selecția"
      showSelectAll={true}
      showClearAll={true}
      maxDisplayItems={2}
      className={className}
    />
  );
}
