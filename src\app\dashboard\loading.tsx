import { Skeleton } from "@/components/ui/skeleton";

export default function DashboardLoading() {
  return (
    <div className="container mx-auto px-4 py-8 min-h-screen max-w-7xl">
      <div className="space-y-6">
        {/* Header skeleton */}
        <div className="flex items-start justify-between flex-wrap gap-4">
          <div className="min-w-0 flex-1">
            <div className="flex items-center gap-3">
              <Skeleton className="h-6 w-6 sm:h-8 sm:w-8 rounded" />
              <Skeleton className="h-8 w-32 sm:w-48" />
            </div>
            <Skeleton className="h-4 w-64 sm:w-96 mt-2" />
          </div>
        </div>

        {/* Portfolio selector skeleton */}
        <div className="space-y-3">
          <Skeleton className="h-5 w-20" />
          <div className="w-full sm:max-w-md">
            <Skeleton className="h-10 w-full" />
          </div>
        </div>

        {/* Performance card skeleton */}
        <div className="bg-card border rounded-lg p-4 sm:p-6">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6">
            <div className="flex items-center gap-3 min-w-0">
              <Skeleton className="h-5 w-5 sm:h-6 sm:w-6 rounded" />
              <Skeleton className="h-6 w-48" />
            </div>
            <div className="flex-shrink-0">
              <div className="flex items-center gap-1 flex-wrap">
                {Array.from({ length: 4 }).map((_, i) => (
                  <Skeleton key={i} className="h-8 w-12" />
                ))}
              </div>
            </div>
          </div>

          {/* Performance summary skeleton */}
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-4">
            <div className="space-y-1 min-w-0">
              <Skeleton className="h-8 w-32" />
              <div className="flex items-center gap-2 flex-wrap">
                <Skeleton className="h-4 w-4 rounded" />
                <Skeleton className="h-4 w-16" />
                <Skeleton className="h-4 w-24" />
              </div>
            </div>
            <Skeleton className="h-4 w-24" />
          </div>

          {/* Chart skeleton */}
          <div className="h-64 sm:h-80 bg-muted/20 rounded-lg flex items-center justify-center">
            <div className="text-center">
              <Skeleton className="h-8 w-8 sm:h-12 sm:w-12 rounded mx-auto mb-4" />
              <Skeleton className="h-4 w-48 mx-auto" />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
