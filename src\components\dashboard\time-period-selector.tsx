"use client";

import { But<PERSON> } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { TimePeriod } from "@/utils/db/portfolio-performance-queries";

interface TimePeriodOption {
  value: TimePeriod;
  label: string;
  description: string;
}

const TIME_PERIOD_OPTIONS: TimePeriodOption[] = [
  {
    value: "1W",
    label: "1W",
    description: "1 săptămână",
  },
  {
    value: "1M",
    label: "1M",
    description: "1 lună",
  },
  {
    value: "YTD",
    label: "YTD",
    description: "De la începutul anului",
  },
  {
    value: "1Y",
    label: "1Y",
    description: "1 an",
  },
];

interface TimePeriodSelectorProps {
  value: TimePeriod;
  onValueChange: (value: TimePeriod) => void;
  disabled?: boolean;
  className?: string;
}

export function TimePeriodSelector({
  value,
  onValueChange,
  disabled = false,
  className,
}: TimePeriodSelectorProps) {
  return (
    <div className={cn("flex items-center gap-1 flex-wrap", className)}>
      {TIME_PERIOD_OPTIONS.map((option) => (
        <Button
          key={option.value}
          variant={value === option.value ? "default" : "outline"}
          size="sm"
          onClick={() => onValueChange(option.value)}
          disabled={disabled}
          className={cn(
            "h-8 px-2 sm:px-3 text-xs font-medium transition-all min-w-[2.5rem]",
            value === option.value
              ? "bg-portavio-orange hover:bg-portavio-orange/90 text-white"
              : "hover:bg-muted"
          )}
          title={option.description}
        >
          {option.label}
        </Button>
      ))}
    </div>
  );
}

// Export the time period options for use in other components
export { TIME_PERIOD_OPTIONS };
export type { TimePeriodOption };
