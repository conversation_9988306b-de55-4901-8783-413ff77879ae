"use client";

import { useQuery } from "@tanstack/react-query";
import {
  TimePeriod,
  PortfolioPerformanceData,
} from "@/utils/db/portfolio-performance-queries";

interface PortfolioPerformanceResponse {
  success: boolean;
  data: PortfolioPerformanceData[];
  metadata: {
    period: TimePeriod;
    portfolios: Array<{
      id: string;
      name: string;
    }>;
    dataPoints: number;
  };
}

interface UsePortfolioPerformanceOptions {
  portfolios: string[];
  period: TimePeriod;
  enabled?: boolean;
}

export function usePortfolioPerformance({
  portfolios,
  period,
  enabled = true,
}: UsePortfolioPerformanceOptions) {
  return useQuery<PortfolioPerformanceResponse, Error>({
    queryKey: ["portfolio-performance", portfolios, period],
    queryFn: async () => {
      const params = new URLSearchParams({
        portfolios: JSON.stringify(portfolios),
        period,
      });

      const response = await fetch(`/api/dashboard/performance?${params}`);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          errorData.error || "Nu s-au putut încărca datele de performanță"
        );
      }

      return response.json();
    },
    enabled: enabled,
    staleTime: 0, // 5 minutes
    gcTime: 0, // 10 minutes
    retry: (failureCount, error) => {
      // Don't retry on 4xx errors
      if (error.message.includes("401") || error.message.includes("403")) {
        return false;
      }
      return failureCount < 3;
    },
  });
}
