"use client";

import { LineChart } from "@/components/ui/line-chart";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import { Button } from "@/components/ui/button";
import { usePortfolioPerformance } from "@/hooks/use-portfolio-performance";
import { TimePeriod } from "@/utils/db/portfolio-performance-queries";
import { AlertCircle, RefreshCw, TrendingDown, TrendingUp } from "lucide-react";
import { useMemo } from "react";

interface PerformanceChartProps {
  portfolios: string[];
  period: TimePeriod;
  className?: string;
}

export function PerformanceChart({
  portfolios,
  period,
  className,
}: PerformanceChartProps) {
  const { data, isLoading, error, refetch } = usePortfolioPerformance({
    portfolios,
    period,
  });

  // Format data for the chart
  const chartData = useMemo(() => {
    if (!data?.data) return [];

    return data.data.map((point) => ({
      date: formatDateForChart(point.date, period),
      "Valoare portofoliu": point.value,
      value: point.value,
      change: point.change,
      changePercent: point.changePercent,
    }));
  }, [data?.data, period]);

  // Calculate summary statistics
  const summaryStats = useMemo(() => {
    if (!data?.data || data.data.length === 0) {
      return {
        currentValue: 0,
        totalChange: 0,
        totalChangePercent: 0,
        isPositive: true,
      };
    }

    const firstValue = data.data[0]?.value || 0;
    const lastValue = data.data[data.data.length - 1]?.value || 0;
    const totalChange = lastValue - firstValue;
    const totalChangePercent =
      firstValue > 0 ? (totalChange / firstValue) * 100 : 0;

    return {
      currentValue: lastValue,
      totalChange,
      totalChangePercent,
      isPositive: totalChange >= 0,
    };
  }, [data?.data]);

  // Value formatter for chart
  const valueFormatter = (value: number) => {
    return new Intl.NumberFormat("ro-RO", {
      style: "currency",
      currency: "EUR",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  // Loading state
  if (isLoading) {
    return (
      <div className={`space-y-4 ${className}`}>
        {/* Summary skeleton */}
        <div className="flex items-center justify-between">
          <div className="space-y-2">
            <div className="h-8 w-32 bg-muted animate-pulse rounded" />
            <div className="h-4 w-24 bg-muted animate-pulse rounded" />
          </div>
          <div className="h-6 w-20 bg-muted animate-pulse rounded" />
        </div>

        {/* Chart skeleton */}
        <div className="h-64 sm:h-80 bg-muted/20 rounded-lg flex items-center justify-center">
          <div className="flex items-center gap-2">
            <LoadingSpinner size="sm" />
            <span className="text-sm text-muted-foreground">
              Se încarcă datele de performanță...
            </span>
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className={`space-y-4 ${className}`}>
        <div className="h-64 sm:h-80 bg-muted/20 rounded-lg flex items-center justify-center">
          <div className="text-center px-4">
            <AlertCircle className="h-8 w-8 sm:h-12 sm:w-12 text-red-500 mx-auto mb-4" />
            <p className="text-red-600 dark:text-red-400 font-medium mb-2 text-sm sm:text-base">
              Eroare la încărcarea datelor
            </p>
            <p className="text-xs sm:text-sm text-muted-foreground mb-4">
              {error.message}
            </p>
            <Button
              variant="outline"
              size="sm"
              onClick={() => refetch()}
              className="gap-2"
            >
              <RefreshCw className="h-4 w-4" />
              Încearcă din nou
            </Button>
          </div>
        </div>
      </div>
    );
  }

  // No data state
  if (!data?.data || data.data.length === 0) {
    return (
      <div className={`space-y-4 ${className}`}>
        <div className="h-64 sm:h-80 bg-muted/20 rounded-lg flex items-center justify-center">
          <div className="text-center px-4">
            <TrendingUp className="h-8 w-8 sm:h-12 sm:w-12 text-muted-foreground mx-auto mb-4" />
            <p className="text-muted-foreground font-medium mb-2 text-sm sm:text-base">
              Nu există date de performanță
            </p>
            <p className="text-xs sm:text-sm text-muted-foreground">
              Nu s-au găsit tranzacții pentru perioada selectată
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Performance Summary */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div className="space-y-1 min-w-0">
          <div className="text-xl sm:text-2xl font-bold truncate">
            {valueFormatter(summaryStats.currentValue)}
          </div>
          <div className="flex items-center gap-2 flex-wrap">
            {summaryStats.isPositive ? (
              <TrendingUp className="h-4 w-4 text-green-600 flex-shrink-0" />
            ) : (
              <TrendingDown className="h-4 w-4 text-red-600 flex-shrink-0" />
            )}
            <span
              className={`text-sm font-medium ${
                summaryStats.isPositive
                  ? "text-green-600 dark:text-green-400"
                  : "text-red-600 dark:text-red-400"
              }`}
            >
              {summaryStats.isPositive ? "+" : ""}
              {summaryStats.totalChangePercent.toFixed(2)}%
            </span>
            <span className="text-sm text-muted-foreground">
              ({summaryStats.isPositive ? "+" : ""}
              {valueFormatter(summaryStats.totalChange)})
            </span>
          </div>
        </div>

        {/* Portfolio info */}
        {data.metadata.portfolios.length > 0 && (
          <div className="text-sm text-muted-foreground text-right sm:text-left">
            {data.metadata.portfolios.length === 1
              ? data.metadata.portfolios[0].name
              : `${data.metadata.portfolios.length} portofolii`}
          </div>
        )}
      </div>

      {/* Chart */}
      <LineChart
        data={chartData}
        index="date"
        categories={["Valoare portofoliu"]}
        colors={["orange"]}
        valueFormatter={valueFormatter}
        showLegend={false}
        showGridLines={true}
        showXAxis={true}
        showYAxis={true}
        autoMinValue={false}
        connectNulls={true}
        className="h-64 sm:h-80"
      />
    </div>
  );
}

// Helper function to format dates for chart display
function formatDateForChart(dateString: string, period: TimePeriod): string {
  const date = new Date(dateString);

  switch (period) {
    case "1W":
    case "1M":
      // Show day and month for short periods
      return date.toLocaleDateString("ro-RO", {
        day: "numeric",
        month: "short",
      });
    case "YTD":
      // Show month and day for YTD
      return date.toLocaleDateString("ro-RO", {
        day: "numeric",
        month: "short",
      });
    case "1Y":
      // Show month and year for 1 year
      return date.toLocaleDateString("ro-RO", {
        month: "short",
        year: "2-digit",
      });
    default:
      return date.toLocaleDateString("ro-RO");
  }
}
